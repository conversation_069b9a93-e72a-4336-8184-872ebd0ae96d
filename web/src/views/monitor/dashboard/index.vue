<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue';
import { NGrid, NGridItem, NButton, NSpace, NSpin, NAlert, NTag } from 'naive-ui';
import SystemOverviewCard from './components/system-overview-card.vue';
import TaskOverviewCard from './components/task-overview-card.vue';
import PerformanceOverviewCard from './components/performance-overview-card.vue';
import ErrorOverviewCard from './components/error-overview-card.vue';
import ResourceOverviewCard from './components/resource-overview-card.vue';
import { useRealtimeData } from './composables/useRealtimeData';
import { fetchAppHealth, fetchBusinessStats, fetchPerformanceData, fetchSystemOverview } from '@/service/api';

// 监控数据状态
const loading = ref(false);
const error = ref<string | null>(null);

// 概览数据
const healthData = ref<Api.Monitor.AppHealthData | null>(null);
const businessData = ref<Api.Monitor.BusinessStatsData | null>(null);
const performanceData = ref<Api.Monitor.PerformanceData | null>(null);
const systemData = ref<Api.Monitor.SystemOverviewData | null>(null);

// SSE实时数据
const {
  isConnected,
  isConnecting,
  realtimeData,
  error: sseError,
  lastUpdateTime,
  connect,
  disconnect,
  reconnect,
  getConnectionStatus
} = useRealtimeData();

// 是否启用实时模式
const realtimeMode = ref(true);

// 自动刷新定时器（作为SSE的备用方案）
let refreshTimer: NodeJS.Timeout | null = null;

onMounted(async () => {
  try {
    // 首次加载概览数据
    await loadOverviewData();

    if (realtimeMode.value) {
      // 启动SSE实时连接
      connect();
    } else {
      // 启动定时器轮询（备用方案）
      startAutoRefresh();
    }
  } catch (err) {
    error.value = err instanceof Error ? err.message : '初始化监控页面失败';
  }
});

onUnmounted(() => {
  // 清理资源
  disconnect();
  if (refreshTimer) {
    clearInterval(refreshTimer);
    refreshTimer = null;
  }
});

// 监听SSE数据变化，更新概览数据
watch(realtimeData, (newData) => {
  if (newData && !newData.error) {
    // 更新概览数据
    healthData.value = newData.health;
    businessData.value = newData.business;
    performanceData.value = newData.performance;
  }
});

// 监听SSE错误
watch(sseError, (newError) => {
  if (newError) {
    error.value = `实时连接错误: ${newError}`;
  }
});

function startAutoRefresh() {
  // 每30秒自动刷新一次（备用方案）
  refreshTimer = setInterval(() => {
    handleRefresh();
  }, 30000);
}

// 加载概览数据
async function loadOverviewData() {
  try {
    loading.value = true;
    error.value = null;

    // 并行加载所有概览数据
    const [healthRes, businessRes, performanceRes, systemRes] = await Promise.allSettled([
      fetchAppHealth(),
      fetchBusinessStats(),
      fetchPerformanceData(60),
      fetchSystemOverview()
    ]);

    // 处理结果
    if (healthRes.status === 'fulfilled') {
      healthData.value = healthRes.value.data;
    }
    if (businessRes.status === 'fulfilled') {
      businessData.value = businessRes.value.data;
    }
    if (performanceRes.status === 'fulfilled') {
      performanceData.value = performanceRes.value.data;
    }
    if (systemRes.status === 'fulfilled') {
      systemData.value = systemRes.value.data;
    }
  } catch (err) {
    error.value = err instanceof Error ? err.message : '加载监控数据失败';
    console.error('加载监控概览数据失败:', err);
  } finally {
    loading.value = false;
  }
}

function handleRefresh() {
  // 刷新概览数据
  loadOverviewData();
}

function toggleRealtimeMode() {
  realtimeMode.value = !realtimeMode.value;

  if (realtimeMode.value) {
    // 切换到实时模式
    if (refreshTimer) {
      clearInterval(refreshTimer);
      refreshTimer = null;
    }
    connect();
  } else {
    // 切换到轮询模式
    disconnect();
    startAutoRefresh();
    handleRefresh(); // 立即刷新一次
  }
}

function handleManualRefresh() {
  if (realtimeMode.value) {
    // 实时模式下重连SSE
    reconnect();
  } else {
    // 轮询模式下手动刷新
    handleRefresh();
  }
}
</script>

<template>
  <div class="monitor-dashboard">
    <!-- 页面头部 -->
    <div class="flex justify-between items-center mb-6">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">系统监控</h1>
        <div class="flex items-center gap-4 mt-1">
          <p class="text-gray-600">实时监控系统关键指标和状态概览</p>

          <!-- 连接状态指示器 -->
          <div class="flex items-center gap-2">
            <NTag
              :type="isConnected ? 'success' : isConnecting ? 'warning' : 'error'"
              size="small"
            >
              {{ getConnectionStatus() }}
            </NTag>

            <span v-if="lastUpdateTime" class="text-xs text-gray-500">
              最后更新: {{ new Date(lastUpdateTime).toLocaleTimeString() }}
            </span>
          </div>
        </div>
      </div>

      <NSpace>
        <NButton
          @click="toggleRealtimeMode"
          :type="realtimeMode ? 'primary' : 'default'"
          size="small"
        >
          {{ realtimeMode ? '实时模式' : '轮询模式' }}
        </NButton>

        <NButton
          @click="handleManualRefresh"
          :loading="loading || isConnecting"
        >
          {{ realtimeMode ? '重连' : '刷新数据' }}
        </NButton>
      </NSpace>
    </div>

    <NSpin :show="loading">
      <div v-if="error" class="mb-4">
        <NAlert type="error" :title="error" closable @close="error = null" />
      </div>

      <NGrid :cols="24" :x-gap="16" :y-gap="16">
        <!-- 系统状态概览 -->
        <NGridItem :span="24" :md-span="12">
          <SystemOverviewCard
            :health-data="healthData"
            :loading="loading"
          />
        </NGridItem>

        <!-- 任务执行概览 -->
        <NGridItem :span="24" :md-span="12">
          <TaskOverviewCard
            :business-data="businessData"
            :loading="loading"
          />
        </NGridItem>

        <!-- 性能概览 -->
        <NGridItem :span="24" :md-span="8">
          <PerformanceOverviewCard
            :performance-data="performanceData"
            :loading="loading"
          />
        </NGridItem>

        <!-- 错误概览 -->
        <NGridItem :span="24" :md-span="8">
          <ErrorOverviewCard
            :loading="loading"
          />
        </NGridItem>

        <!-- 资源概览 -->
        <NGridItem :span="24" :md-span="8">
          <ResourceOverviewCard
            :system-data="systemData"
            :loading="loading"
          />
        </NGridItem>
      </NGrid>
    </NSpin>

    <!-- 自动刷新提示 -->
    <div class="fixed bottom-4 right-4 text-xs text-gray-500 bg-white px-3 py-2 rounded-lg shadow-sm border">
      自动刷新: 每30秒
    </div>
  </div>
</template>

<style scoped>
@import './styles/overview-card.css';

.monitor-dashboard {
  padding: 16px;
  min-height: calc(100vh - 64px);
}

.h-full {
  height: 100%;
}

.monitor-dashboard :deep(.overview-card) {
  height: 100%;
}
</style>
