<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { NCard, NGrid, NGridItem, NStatistic, NSpin, NAlert, NProgress, NSpace, NTag } from 'naive-ui';
import { fetchSystemOverview } from '@/service/api';

// 系统资源数据
const loading = ref(true);
const error = ref<string | null>(null);
const systemData = ref<Api.Monitor.SystemOverviewData | null>(null);

onMounted(async () => {
  await loadSystemData();
});

async function loadSystemData() {
  try {
    loading.value = true;
    error.value = null;

    // 调用监控API获取系统资源数据
    const { data } = await fetchSystemOverview();
    systemData.value = data;
  } catch (err) {
    error.value = err instanceof Error ? err.message : '加载系统资源数据失败';
    console.error('加载系统资源数据失败:', err);
  } finally {
    loading.value = false;
  }
}

// 格式化字节大小
function formatBytes(bytes: number, decimals = 2) {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];
  
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

// 格式化运行时间
function formatUptime(seconds: number) {
  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  
  if (days > 0) {
    return `${days}天 ${hours}小时 ${minutes}分钟`;
  } else if (hours > 0) {
    return `${hours}小时 ${minutes}分钟`;
  } else {
    return `${minutes}分钟`;
  }
}

// 获取使用率颜色
function getUsageColor(percent: number) {
  if (percent < 50) return '#18a058';
  if (percent < 80) return '#f0a020';
  return '#d03050';
}

// 获取使用率状态
function getUsageStatus(percent: number) {
  if (percent < 50) return 'success';
  if (percent < 80) return 'warning';
  return 'error';
}

// 计算网络速度（简单估算，实际应该基于时间间隔）
const networkStats = computed(() => {
  if (!systemData.value?.network) return null;
  
  const { bytes_sent, bytes_recv, packets_sent, packets_recv } = systemData.value.network;
  
  return {
    totalBytes: bytes_sent + bytes_recv,
    totalPackets: packets_sent + packets_recv,
    sentFormatted: formatBytes(bytes_sent),
    recvFormatted: formatBytes(bytes_recv)
  };
});

// 外部数据更新方法
function updateData(newData: Api.Monitor.SystemOverviewData) {
  systemData.value = newData;
  error.value = null;
}

// 暴露方法供父组件调用
defineExpose({
  loadSystemData,
  updateData
});
</script>

<template>
  <NCard title="系统资源监控" class="monitor-card">
    <NSpin :show="loading">
      <div v-if="error" class="mb-4">
        <NAlert type="error" :title="error" />
      </div>

      <div v-if="systemData && !systemData.error" class="space-y-6">
        <!-- CPU监控 -->
        <div v-if="systemData.cpu">
          <h3 class="text-lg font-semibold mb-4">CPU使用情况</h3>
          <NGrid :cols="3" :x-gap="16" :y-gap="16">
            <NGridItem>
              <div class="text-center">
                <div class="text-2xl font-bold mb-2">{{ systemData.cpu.percent }}%</div>
                <NProgress
                  :percentage="systemData.cpu.percent"
                  :color="getUsageColor(systemData.cpu.percent)"
                  :show-indicator="false"
                  class="mb-2"
                />
                <div class="text-sm text-gray-600">CPU使用率</div>
              </div>
            </NGridItem>
            <NGridItem>
              <NStatistic
                label="物理核心"
                :value="systemData.cpu.cores_physical"
              />
            </NGridItem>
            <NGridItem>
              <NStatistic
                label="逻辑核心"
                :value="systemData.cpu.cores_logical"
              />
            </NGridItem>
          </NGrid>
          
          <div v-if="systemData.cpu.load_avg.length > 0" class="mt-4">
            <h4 class="font-medium mb-2">负载平均值</h4>
            <NSpace>
              <NTag v-for="(load, index) in systemData.cpu.load_avg" :key="index" type="info">
                {{ ['1分钟', '5分钟', '15分钟'][index] }}: {{ load }}
              </NTag>
            </NSpace>
          </div>
        </div>

        <!-- 内存监控 -->
        <div v-if="systemData.memory">
          <h3 class="text-lg font-semibold mb-4">内存使用情况</h3>
          <NGrid :cols="2" :x-gap="16" :y-gap="16">
            <NGridItem>
              <div class="text-center">
                <div class="text-2xl font-bold mb-2">{{ systemData.memory.percent }}%</div>
                <NProgress
                  :percentage="systemData.memory.percent"
                  :color="getUsageColor(systemData.memory.percent)"
                  :show-indicator="false"
                  class="mb-2"
                />
                <div class="text-sm text-gray-600">内存使用率</div>
              </div>
            </NGridItem>
            <NGridItem>
              <div class="space-y-2">
                <div class="flex justify-between">
                  <span>总内存:</span>
                  <span class="font-semibold">{{ formatBytes(systemData.memory.total) }}</span>
                </div>
                <div class="flex justify-between">
                  <span>已使用:</span>
                  <span class="font-semibold">{{ formatBytes(systemData.memory.used) }}</span>
                </div>
                <div class="flex justify-between">
                  <span>可用:</span>
                  <span class="font-semibold">{{ formatBytes(systemData.memory.available) }}</span>
                </div>
                <div class="flex justify-between">
                  <span>缓存:</span>
                  <span class="font-semibold">{{ formatBytes(systemData.memory.cached) }}</span>
                </div>
              </div>
            </NGridItem>
          </NGrid>
        </div>

        <!-- 交换分区监控 -->
        <div v-if="systemData.swap && systemData.swap.total > 0">
          <h3 class="text-lg font-semibold mb-4">交换分区</h3>
          <NGrid :cols="2" :x-gap="16" :y-gap="16">
            <NGridItem>
              <div class="text-center">
                <div class="text-2xl font-bold mb-2">{{ systemData.swap.percent }}%</div>
                <NProgress
                  :percentage="systemData.swap.percent"
                  :color="getUsageColor(systemData.swap.percent)"
                  :show-indicator="false"
                  class="mb-2"
                />
                <div class="text-sm text-gray-600">交换分区使用率</div>
              </div>
            </NGridItem>
            <NGridItem>
              <div class="space-y-2">
                <div class="flex justify-between">
                  <span>总大小:</span>
                  <span class="font-semibold">{{ formatBytes(systemData.swap.total) }}</span>
                </div>
                <div class="flex justify-between">
                  <span>已使用:</span>
                  <span class="font-semibold">{{ formatBytes(systemData.swap.used) }}</span>
                </div>
                <div class="flex justify-between">
                  <span>空闲:</span>
                  <span class="font-semibold">{{ formatBytes(systemData.swap.free) }}</span>
                </div>
              </div>
            </NGridItem>
          </NGrid>
        </div>

        <!-- 磁盘监控 -->
        <div v-if="systemData.disk">
          <h3 class="text-lg font-semibold mb-4">磁盘使用情况</h3>
          <NGrid :cols="2" :x-gap="16" :y-gap="16">
            <NGridItem>
              <div class="text-center">
                <div class="text-2xl font-bold mb-2">{{ systemData.disk.percent }}%</div>
                <NProgress
                  :percentage="systemData.disk.percent"
                  :color="getUsageColor(systemData.disk.percent)"
                  :show-indicator="false"
                  class="mb-2"
                />
                <div class="text-sm text-gray-600">磁盘使用率</div>
              </div>
            </NGridItem>
            <NGridItem>
              <div class="space-y-2">
                <div class="flex justify-between">
                  <span>总容量:</span>
                  <span class="font-semibold">{{ formatBytes(systemData.disk.total) }}</span>
                </div>
                <div class="flex justify-between">
                  <span>已使用:</span>
                  <span class="font-semibold">{{ formatBytes(systemData.disk.used) }}</span>
                </div>
                <div class="flex justify-between">
                  <span>可用:</span>
                  <span class="font-semibold">{{ formatBytes(systemData.disk.free) }}</span>
                </div>
              </div>
            </NGridItem>
          </NGrid>
        </div>

        <!-- 网络监控 -->
        <div v-if="systemData.network && networkStats">
          <h3 class="text-lg font-semibold mb-4">网络I/O统计</h3>
          <NGrid :cols="3" :x-gap="16" :y-gap="16">
            <NGridItem>
              <NStatistic
                label="发送数据"
                :value="networkStats.sentFormatted"
              />
            </NGridItem>
            <NGridItem>
              <NStatistic
                label="接收数据"
                :value="networkStats.recvFormatted"
              />
            </NGridItem>
            <NGridItem>
              <NStatistic
                label="网络连接数"
                :value="systemData.network.connections"
              />
            </NGridItem>
          </NGrid>
        </div>

        <!-- 系统信息 -->
        <div v-if="systemData.system">
          <h3 class="text-lg font-semibold mb-4">系统信息</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="space-y-2">
              <div class="flex justify-between">
                <span>系统平台:</span>
                <span class="font-semibold">{{ systemData.system.platform }}</span>
              </div>
              <div class="flex justify-between">
                <span>系统版本:</span>
                <span class="font-semibold">{{ systemData.system.platform_release }}</span>
              </div>
              <div class="flex justify-between">
                <span>架构:</span>
                <span class="font-semibold">{{ systemData.system.architecture }}</span>
              </div>
            </div>
            <div class="space-y-2">
              <div class="flex justify-between">
                <span>进程数:</span>
                <span class="font-semibold">{{ systemData.system.process_count }}</span>
              </div>
              <div class="flex justify-between">
                <span>运行时间:</span>
                <span class="font-semibold">{{ formatUptime(systemData.system.uptime_seconds) }}</span>
              </div>
              <div class="flex justify-between">
                <span>启动时间:</span>
                <span class="font-semibold">{{ new Date(systemData.system.boot_time).toLocaleString() }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 更新时间 -->
        <div v-if="systemData.timestamp" class="text-sm text-gray-500 text-center">
          最后更新: {{ new Date(systemData.timestamp).toLocaleString() }}
        </div>
      </div>

      <!-- 系统资源监控不可用 -->
      <div v-else-if="systemData && systemData.error" class="text-center py-8">
        <NAlert type="warning" :title="systemData.error" />
        <p class="text-gray-600 mt-4">
          系统资源监控功能需要安装psutil依赖包。<br>
          请运行: <code class="bg-gray-100 px-2 py-1 rounded">pip install psutil</code>
        </p>
      </div>
    </NSpin>
  </NCard>
</template>

<style scoped>
.monitor-card {
  height: 100%;
}

code {
  font-family: 'Courier New', monospace;
}
</style>
