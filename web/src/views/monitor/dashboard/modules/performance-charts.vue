<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { NCard, NGrid, NGridItem, NSpin, NAlert, NStatistic } from 'naive-ui';
import { fetchPerformanceData } from '@/service/api';

// 性能监控数据
const loading = ref(true);
const error = ref<string | null>(null);
const performanceData = ref<Api.Monitor.PerformanceData | null>(null);

onMounted(async () => {
  await loadPerformanceData();
});

async function loadPerformanceData() {
  try {
    loading.value = true;
    error.value = null;

    // 调用监控API获取性能数据
    const { data } = await fetchPerformanceData(60); // 获取最近60分钟的数据
    performanceData.value = data;
  } catch (err) {
    error.value = err instanceof Error ? err.message : '加载性能数据失败';
    console.error('加载性能监控数据失败:', err);
  } finally {
    loading.value = false;
  }
}

// 外部数据更新方法
function updateData(newData: Api.Monitor.PerformanceData) {
  performanceData.value = newData;
  error.value = null;
}

// 暴露方法供父组件调用
defineExpose({
  loadPerformanceData,
  updateData
});

function formatBytes(bytes: number) {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function getStatusCodeColor(code: string) {
  if (code.startsWith('2')) return 'success';
  if (code.startsWith('3')) return 'info';
  if (code.startsWith('4')) return 'warning';
  if (code.startsWith('5')) return 'error';
  return 'default';
}

function getResourceUsageColor(usage: number) {
  if (usage < 50) return 'success';
  if (usage < 80) return 'warning';
  return 'error';
}
</script>

<template>
  <NCard title="性能监控" class="h-full">
    <NSpin :show="loading">
      <div v-if="error" class="mb-4">
        <NAlert type="error" :title="error" closable @close="error = null" />
      </div>

      <div v-if="performanceData" class="space-y-6">
        <!-- API性能概览 -->
        <div>
          <h3 class="text-lg font-semibold mb-4">API性能概览</h3>
          <NGrid :cols="4" :x-gap="16" :y-gap="16">
            <NGridItem>
              <NStatistic
                label="平均响应时间"
                :value="`${performanceData.api_performance.avg_response_time.toFixed(2)}s`"
              />
            </NGridItem>
            <NGridItem>
              <NStatistic label="总请求数" :value="performanceData.api_performance.request_count" />
            </NGridItem>
            <NGridItem>
              <NStatistic
                label="错误率"
                :value="`${(performanceData.api_performance.error_rate * 100).toFixed(2)}%`"
              />
            </NGridItem>
            <NGridItem>
              <NStatistic
                label="每分钟请求"
                :value="performanceData.api_performance.requests_per_minute.toFixed(1)"
              />
            </NGridItem>
          </NGrid>
        </div>

        <!-- 最慢端点 -->
        <div>
          <h3 class="text-lg font-semibold mb-4">最慢API端点</h3>
          <div class="space-y-2">
            <div
              v-for="(endpoint, index) in performanceData.api_performance.slowest_endpoints"
              :key="index"
              class="flex justify-between items-center p-3 bg-gray-50 rounded-lg"
            >
              <div class="flex-1">
                <div class="text-sm font-mono">{{ endpoint.endpoint }}</div>
                <div class="text-xs text-gray-500">请求次数: {{ endpoint.count }}</div>
              </div>
              <div class="text-right">
                <div class="text-sm font-semibold">平均: {{ endpoint.avg_time.toFixed(2) }}s</div>
                <div class="text-xs text-gray-500">最大: {{ endpoint.max_time.toFixed(2) }}s</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 状态码分布 -->
        <div>
          <h3 class="text-lg font-semibold mb-4">HTTP状态码分布</h3>
          <NGrid :cols="6" :x-gap="16" :y-gap="16">
            <NGridItem
              v-for="(count, code) in performanceData.api_performance.status_code_distribution"
              :key="code"
            >
              <div class="text-center p-3 border rounded-lg">
                <div class="text-2xl font-bold" :class="{
                  'text-green-600': code.startsWith('2'),
                  'text-blue-600': code.startsWith('3'),
                  'text-yellow-600': code.startsWith('4'),
                  'text-red-600': code.startsWith('5')
                }">
                  {{ count }}
                </div>
                <div class="text-sm text-gray-600">{{ code }}</div>
              </div>
            </NGridItem>
          </NGrid>
        </div>

        <!-- 数据库性能 -->
        <div>
          <h3 class="text-lg font-semibold mb-4">数据库性能</h3>
          <NGrid :cols="3" :x-gap="16" :y-gap="16">
            <NGridItem>
              <NStatistic
                label="平均查询时间"
                :value="`${performanceData.database_performance.query_stats.avg_query_time.toFixed(2)}s`"
              />
            </NGridItem>
            <NGridItem>
              <NStatistic label="慢查询数" :value="performanceData.database_performance.query_stats.slow_queries" />
            </NGridItem>
            <NGridItem>
              <NStatistic label="总查询数" :value="performanceData.database_performance.query_stats.total_queries" />
            </NGridItem>
          </NGrid>
        </div>

        <!-- 系统资源 -->
        <div v-if="performanceData.system_resources.available">
          <h3 class="text-lg font-semibold mb-4">系统资源使用</h3>
          <NGrid :cols="3" :x-gap="16" :y-gap="16">
            <NGridItem>
              <div class="text-center p-4 border rounded-lg">
                <div class="text-3xl font-bold mb-2" :class="{
                  'text-green-600': performanceData.system_resources.memory_usage < 50,
                  'text-yellow-600': performanceData.system_resources.memory_usage >= 50 && performanceData.system_resources.memory_usage < 80,
                  'text-red-600': performanceData.system_resources.memory_usage >= 80
                }">
                  {{ performanceData.system_resources.memory_usage.toFixed(1) }}%
                </div>
                <div class="text-sm text-gray-600 mb-1">内存使用率</div>
                <div class="text-xs text-gray-500">
                  {{ formatBytes(performanceData.system_resources.memory_total - performanceData.system_resources.memory_available) }} /
                  {{ formatBytes(performanceData.system_resources.memory_total) }}
                </div>
              </div>
            </NGridItem>
            <NGridItem>
              <div class="text-center p-4 border rounded-lg">
                <div class="text-3xl font-bold mb-2" :class="{
                  'text-green-600': performanceData.system_resources.cpu_usage < 50,
                  'text-yellow-600': performanceData.system_resources.cpu_usage >= 50 && performanceData.system_resources.cpu_usage < 80,
                  'text-red-600': performanceData.system_resources.cpu_usage >= 80
                }">
                  {{ performanceData.system_resources.cpu_usage.toFixed(1) }}%
                </div>
                <div class="text-sm text-gray-600">CPU使用率</div>
              </div>
            </NGridItem>
            <NGridItem>
              <div class="text-center p-4 border rounded-lg">
                <div class="text-3xl font-bold mb-2" :class="{
                  'text-green-600': performanceData.system_resources.disk_usage < 50,
                  'text-yellow-600': performanceData.system_resources.disk_usage >= 50 && performanceData.system_resources.disk_usage < 80,
                  'text-red-600': performanceData.system_resources.disk_usage >= 80
                }">
                  {{ performanceData.system_resources.disk_usage.toFixed(1) }}%
                </div>
                <div class="text-sm text-gray-600 mb-1">磁盘使用率</div>
                <div class="text-xs text-gray-500">
                  {{ formatBytes(performanceData.system_resources.disk_total - performanceData.system_resources.disk_free) }} /
                  {{ formatBytes(performanceData.system_resources.disk_total) }}
                </div>
              </div>
            </NGridItem>
          </NGrid>
        </div>
      </div>

      <div v-else-if="!loading" class="text-center text-gray-500 py-8">
        暂无性能监控数据
      </div>
    </NSpin>
  </NCard>
</template>

<style scoped>
.space-y-6 > * + * {
  margin-top: 1.5rem;
}

.space-y-2 > * + * {
  margin-top: 0.5rem;
}

.h-full {
  height: 100%;
}
</style>
