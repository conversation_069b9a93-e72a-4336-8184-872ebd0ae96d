<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { NCard, NGrid, NGridItem, NStatistic, NSpin, NAlert } from 'naive-ui';
import { fetchBusinessStats } from '@/service/api';

// 业务统计数据
const loading = ref(true);
const error = ref<string | null>(null);
const businessData = ref<Api.Monitor.BusinessStatsData | null>(null);

onMounted(async () => {
  await loadBusinessData();
});

async function loadBusinessData() {
  try {
    loading.value = true;
    error.value = null;

    // 调用监控API获取业务统计数据
    const { data } = await fetchBusinessStats();
    businessData.value = data;
  } catch (err) {
    error.value = err instanceof Error ? err.message : '加载业务数据失败';
    console.error('加载业务统计数据失败:', err);
  } finally {
    loading.value = false;
  }
}

// 外部数据更新方法
function updateData(newData: Api.Monitor.BusinessStatsData) {
  businessData.value = newData;
  error.value = null;
}

// 暴露方法供父组件调用
defineExpose({
  loadBusinessData,
  updateData
});

function formatDuration(seconds: number) {
  if (seconds < 60) return `${seconds.toFixed(1)}秒`;
  if (seconds < 3600) return `${(seconds / 60).toFixed(1)}分钟`;
  return `${(seconds / 3600).toFixed(1)}小时`;
}
</script>

<template>
  <NCard title="业务统计" class="h-full">
    <NSpin :show="loading">
      <div v-if="error" class="mb-4">
        <NAlert type="error" :title="error" closable @close="error = null" />
      </div>

      <div v-if="businessData" class="space-y-6">
        <!-- STRM任务统计 -->
        <div>
          <h3 class="text-lg font-semibold mb-4">STRM任务统计</h3>
          <NGrid :cols="4" :x-gap="16" :y-gap="16">
            <NGridItem>
              <NStatistic label="总任务数" :value="businessData.strm_tasks.total" />
            </NGridItem>
            <NGridItem>
              <NStatistic label="已完成" :value="businessData.strm_tasks.completed" />
            </NGridItem>
            <NGridItem>
              <NStatistic label="运行中" :value="businessData.strm_tasks.running" />
            </NGridItem>
            <NGridItem>
              <NStatistic
                label="成功率"
                :value="`${(businessData.strm_tasks.success_rate * 100).toFixed(1)}%`"
              />
            </NGridItem>
          </NGrid>

          <div class="mt-4">
            <NGrid :cols="2" :x-gap="16">
              <NGridItem>
                <NStatistic label="失败任务" :value="businessData.strm_tasks.failed" />
              </NGridItem>
              <NGridItem>
                <NStatistic
                  label="平均处理时间"
                  :value="formatDuration(businessData.strm_tasks.avg_processing_time)"
                />
              </NGridItem>
            </NGrid>
          </div>
        </div>

        <!-- 用户活动统计 -->
        <div>
          <h3 class="text-lg font-semibold mb-4">用户活动统计</h3>
          <NGrid :cols="4" :x-gap="16" :y-gap="16">
            <NGridItem>
              <NStatistic label="总用户数" :value="businessData.user_activity.total_users" />
            </NGridItem>
            <NGridItem>
              <NStatistic label="今日活跃" :value="businessData.user_activity.active_today" />
            </NGridItem>
            <NGridItem>
              <NStatistic label="今日登录" :value="businessData.user_activity.login_count_today" />
            </NGridItem>
            <NGridItem>
              <NStatistic label="本周活跃" :value="businessData.user_activity.active_users_week" />
            </NGridItem>
          </NGrid>
        </div>

        <!-- API请求统计 -->
        <div>
          <h3 class="text-lg font-semibold mb-4">API请求统计</h3>
          <NGrid :cols="3" :x-gap="16" :y-gap="16" class="mb-4">
            <NGridItem>
              <NStatistic label="今日请求总数" :value="businessData.api_requests.total_requests_today" />
            </NGridItem>
            <NGridItem>
              <NStatistic
                label="平均响应时间"
                :value="`${businessData.api_requests.avg_response_time.toFixed(2)}s`"
              />
            </NGridItem>
            <NGridItem>
              <NStatistic label="错误请求数" :value="businessData.api_requests.error_count" />
            </NGridItem>
          </NGrid>

          <!-- 热门端点 -->
          <div>
            <h4 class="text-md font-medium mb-3">热门API端点</h4>
            <div class="space-y-2">
              <div
                v-for="(endpoint, index) in businessData.api_requests.top_endpoints"
                :key="index"
                class="flex justify-between items-center p-3 bg-gray-50 rounded-lg"
              >
                <span class="text-sm font-mono">{{ endpoint.endpoint }}</span>
                <span class="text-sm font-semibold text-blue-600">{{ endpoint.count }} 次</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div v-else-if="!loading" class="text-center text-gray-500 py-8">
        暂无业务统计数据
      </div>
    </NSpin>
  </NCard>
</template>

<style scoped>
.space-y-6 > * + * {
  margin-top: 1.5rem;
}

.space-y-2 > * + * {
  margin-top: 0.5rem;
}

.h-full {
  height: 100%;
}
</style>
