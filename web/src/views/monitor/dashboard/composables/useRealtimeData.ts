import { ref, onUnmounted } from 'vue';
import { getServiceBaseURL } from '@/utils/service';
import { localStg } from '@/utils/storage';

/**
 * SSE实时数据接收 Composable
 *
 * 提供Server-Sent Events连接管理和实时数据接收功能
 */
export function useRealtimeData() {
  const eventSource = ref<EventSource | null>(null);
  const isConnected = ref(false);
  const isConnecting = ref(false);
  const realtimeData = ref<any>(null);
  const error = ref<string | null>(null);
  const lastUpdateTime = ref<string | null>(null);

  // 重连配置
  const maxRetries = 5;
  const retryDelay = 3000; // 3秒
  let retryCount = 0;
  let retryTimer: NodeJS.Timeout | null = null;

  // 获取正确的服务基础URL
  const isHttpProxy = import.meta.env.DEV && import.meta.env.VITE_HTTP_PROXY === 'Y';
  const { baseURL } = getServiceBaseURL(import.meta.env, isHttpProxy);

  /**
   * 连接SSE流
   */
  const connect = () => {
    if (isConnecting.value || isConnected.value) {
      return;
    }

    try {
      isConnecting.value = true;
      error.value = null;

      // 创建EventSource连接，添加token参数
      const token = localStg.get('token');
      let sseUrl = baseURL ? `${baseURL}/monitor/realtime-stream` : '/api/v1/monitor/realtime-stream';

      // 如果有token，添加到URL参数中
      if (token) {
        const separator = sseUrl.includes('?') ? '&' : '?';
        sseUrl += `${separator}token=${encodeURIComponent(token)}`;
      }

      eventSource.value = new EventSource(sseUrl);

      // 连接打开事件
      eventSource.value.onopen = () => {
        isConnected.value = true;
        isConnecting.value = false;
        retryCount = 0; // 重置重试计数
        console.log('SSE连接已建立');
      };

      // 接收消息事件
      eventSource.value.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);

          if (data.error) {
            // 服务端错误
            error.value = `服务端错误: ${data.error}`;
            console.error('SSE服务端错误:', data.error);
          } else {
            // 正常数据
            realtimeData.value = data;
            lastUpdateTime.value = data.timestamp;
            error.value = null;
          }
        } catch (err) {
          error.value = '数据解析失败';
          console.error('SSE数据解析错误:', err);
        }
      };

      // 连接错误事件
      eventSource.value.onerror = (event) => {
        isConnected.value = false;
        isConnecting.value = false;

        if (eventSource.value?.readyState === EventSource.CLOSED) {
          error.value = 'SSE连接已关闭';
          console.warn('SSE连接已关闭');

          // 尝试重连
          attemptReconnect();
        } else {
          error.value = 'SSE连接错误';
          console.error('SSE连接错误:', event);
        }
      };

    } catch (err) {
      isConnecting.value = false;
      error.value = err instanceof Error ? err.message : 'SSE连接失败';
      console.error('SSE连接失败:', err);
    }
  };

  /**
   * 断开SSE连接
   */
  const disconnect = () => {
    if (retryTimer) {
      clearTimeout(retryTimer);
      retryTimer = null;
    }

    if (eventSource.value) {
      eventSource.value.close();
      eventSource.value = null;
    }

    isConnected.value = false;
    isConnecting.value = false;
    retryCount = 0;
    console.log('SSE连接已断开');
  };

  /**
   * 尝试重连
   */
  const attemptReconnect = () => {
    if (retryCount >= maxRetries) {
      error.value = `重连失败，已达到最大重试次数 (${maxRetries})`;
      console.error('SSE重连失败，已达到最大重试次数');
      return;
    }

    retryCount++;
    error.value = `连接断开，正在重连... (${retryCount}/${maxRetries})`;

    retryTimer = setTimeout(() => {
      console.log(`SSE重连尝试 ${retryCount}/${maxRetries}`);
      disconnect(); // 确保清理旧连接
      connect();
    }, retryDelay);
  };

  /**
   * 手动重连
   */
  const reconnect = () => {
    retryCount = 0;
    disconnect();
    connect();
  };

  /**
   * 获取连接状态文本
   */
  const getConnectionStatus = () => {
    if (isConnecting.value) return '连接中...';
    if (isConnected.value) return '已连接';
    if (error.value) return '连接错误';
    return '未连接';
  };

  // 组件卸载时自动断开连接
  onUnmounted(() => {
    disconnect();
  });

  return {
    // 状态
    isConnected,
    isConnecting,
    realtimeData,
    error,
    lastUpdateTime,

    // 方法
    connect,
    disconnect,
    reconnect,
    getConnectionStatus,

    // 只读状态
    connectionStatus: getConnectionStatus
  };
}
