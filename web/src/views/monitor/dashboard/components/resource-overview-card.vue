<script setup lang="ts">
import { ref, computed } from 'vue';
import { NCard, NProgress, NButton, NSpace, NIcon, NTag } from 'naive-ui';
import { useRouter } from 'vue-router';
import { ArrowRight } from '@vicons/carbon';

interface Props {
  systemData?: Api.Monitor.SystemOverviewData | null;
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  systemData: null,
  loading: false
});

const router = useRouter();

// 计算资源使用情况
const resourceStats = computed(() => {
  if (!props.systemData || !props.systemData.system_resources.available) {
    return {
      cpuUsage: 0,
      memoryUsage: 0,
      diskUsage: 0,
      available: false
    };
  }

  const resources = props.systemData.system_resources;
  return {
    cpuUsage: resources.cpu_usage || 0,
    memoryUsage: resources.memory_usage?.percent || 0,
    diskUsage: resources.disk_usage?.percent || 0,
    available: resources.available
  };
});

// 获取使用率状态
function getUsageStatus(usage: number) {
  if (usage <= 60) return { type: 'success', color: '#52c41a' };
  if (usage <= 80) return { type: 'warning', color: '#faad14' };
  return { type: 'error', color: '#ff4d4f' };
}

// 格式化内存大小
function formatBytes(bytes: number) {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function goToSystemDetail() {
  router.push('/monitor/system');
}
</script>

<template>
  <NCard title="资源概览" class="overview-card resource-overview-card">
    <template #header-extra>
      <NButton text @click="goToSystemDetail">
        <template #icon>
          <NIcon><ArrowRight /></NIcon>
        </template>
        查看详情
      </NButton>
    </template>

    <div v-if="!resourceStats.available" class="text-center py-8">
      <NTag type="warning">系统资源监控不可用</NTag>
      <p class="text-sm text-gray-500 mt-2">请安装 psutil 库以启用资源监控</p>
    </div>

    <div v-else class="space-y-4">
      <!-- CPU 使用率 -->
      <div class="space-y-2">
        <div class="flex justify-between items-center">
          <span class="text-sm font-medium text-gray-700">CPU 使用率</span>
          <NTag :type="getUsageStatus(resourceStats.cpuUsage).type" size="small">
            {{ resourceStats.cpuUsage.toFixed(1) }}%
          </NTag>
        </div>
        <NProgress
          :percentage="resourceStats.cpuUsage"
          :color="getUsageStatus(resourceStats.cpuUsage).color"
          :show-indicator="false"
        />
      </div>

      <!-- 内存使用率 -->
      <div class="space-y-2">
        <div class="flex justify-between items-center">
          <span class="text-sm font-medium text-gray-700">内存使用率</span>
          <NTag :type="getUsageStatus(resourceStats.memoryUsage).type" size="small">
            {{ resourceStats.memoryUsage.toFixed(1) }}%
          </NTag>
        </div>
        <NProgress
          :percentage="resourceStats.memoryUsage"
          :color="getUsageStatus(resourceStats.memoryUsage).color"
          :show-indicator="false"
        />
      </div>

      <!-- 磁盘使用率 -->
      <div class="space-y-2">
        <div class="flex justify-between items-center">
          <span class="text-sm font-medium text-gray-700">磁盘使用率</span>
          <NTag :type="getUsageStatus(resourceStats.diskUsage).type" size="small">
            {{ resourceStats.diskUsage.toFixed(1) }}%
          </NTag>
        </div>
        <NProgress
          :percentage="resourceStats.diskUsage"
          :color="getUsageStatus(resourceStats.diskUsage).color"
          :show-indicator="false"
        />
      </div>

      <!-- 内存详情 -->
      <div v-if="systemData?.system_resources.memory_usage" class="text-center text-sm text-gray-600">
        已用: {{ formatBytes(systemData.system_resources.memory_usage.used) }} /
        总计: {{ formatBytes(systemData.system_resources.memory_usage.total) }}
      </div>
    </div>

    <!-- 快速操作 -->
    <div class="mt-6 pt-4 border-t border-gray-200">
      <NSpace justify="center">
        <NButton @click="goToSystemDetail" type="primary" ghost>
          查看系统详情
        </NButton>
      </NSpace>
    </div>
  </NCard>
</template>

<style scoped>
.resource-overview-card {
  height: 100%;
}
</style>
