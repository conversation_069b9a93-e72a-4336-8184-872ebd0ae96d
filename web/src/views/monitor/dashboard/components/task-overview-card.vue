<script setup lang="ts">
import { ref, computed } from 'vue';
import { NCard, NStatistic, NProgress, NButton, NSpace, NIcon, NTag } from 'naive-ui';
import { useRouter } from 'vue-router';
import { ArrowRight } from '@vicons/carbon';

interface Props {
  businessData?: Api.Monitor.BusinessStatsData | null;
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  businessData: null,
  loading: false
});

const router = useRouter();

// 计算任务统计
const taskStats = computed(() => {
  if (!props.businessData) {
    return {
      total: 0,
      completed: 0,
      running: 0,
      failed: 0,
      successRate: 0
    };
  }

  const tasks = props.businessData.strm_tasks;
  return {
    total: tasks.total,
    completed: tasks.completed,
    running: tasks.running,
    failed: tasks.total - tasks.completed - tasks.running,
    successRate: tasks.success_rate * 100
  };
});

// 成功率状态
const successRateStatus = computed(() => {
  const rate = taskStats.value.successRate;
  if (rate >= 95) return { type: 'success', color: '#52c41a' };
  if (rate >= 80) return { type: 'warning', color: '#faad14' };
  return { type: 'error', color: '#ff4d4f' };
});

function goToTaskDetail() {
  router.push('/monitor/tasks');
}
</script>

<template>
  <NCard title="任务执行概览" class="overview-card task-overview-card">
    <template #header-extra>
      <NButton text @click="goToTaskDetail">
        <template #icon>
          <NIcon><ArrowRight /></NIcon>
        </template>
        查看详情
      </NButton>
    </template>

    <div class="space-y-4">
      <!-- 任务统计 -->
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div class="text-center">
          <NStatistic
            :value="taskStats.total"
            label="总任务数"
            class="text-center"
          />
        </div>
        <div class="text-center">
          <NStatistic
            :value="taskStats.completed"
            label="已完成"
            class="text-center"
          />
        </div>
        <div class="text-center">
          <NStatistic
            :value="taskStats.running"
            label="运行中"
            class="text-center"
          />
        </div>
        <div class="text-center">
          <NStatistic
            :value="taskStats.failed"
            label="失败"
            class="text-center"
          />
        </div>
      </div>

      <!-- 成功率进度条 -->
      <div class="space-y-2">
        <div class="flex justify-between items-center">
          <span class="text-sm font-medium text-gray-700">成功率</span>
          <NTag :type="successRateStatus.type" size="small">
            {{ taskStats.successRate.toFixed(1) }}%
          </NTag>
        </div>
        <NProgress
          :percentage="taskStats.successRate"
          :color="successRateStatus.color"
          :show-indicator="false"
        />
      </div>
    </div>

    <!-- 快速操作 -->
    <div class="mt-6 pt-4 border-t border-gray-200">
      <NSpace justify="center">
        <NButton @click="goToTaskDetail" type="primary" ghost>
          查看任务详情
        </NButton>
      </NSpace>
    </div>
  </NCard>
</template>

<style scoped>
.task-overview-card {
  height: 100%;
}

.task-overview-card :deep(.n-statistic-value) {
  font-size: 1.25rem;
  font-weight: 600;
}

.task-overview-card :deep(.n-statistic-label) {
  font-size: 0.875rem;
  color: #6b7280;
}
</style>
