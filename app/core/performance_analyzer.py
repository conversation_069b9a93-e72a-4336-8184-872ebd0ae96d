"""
性能分析器

基于现有APILog模型的性能数据分析器，提供API性能统计、慢查询分析等功能。
"""

from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from tortoise.functions import Count, Avg, Max, Min
from tortoise.expressions import Q
from app.models.system import APILog
from app.models.strm import StrmTask, TaskStatus


class PerformanceAnalyzer:
    """基于现有APILog模型的性能分析器"""

    @staticmethod
    async def get_performance_stats(minutes: int = 60) -> Dict[str, Any]:
        """
        获取性能统计数据（基于APILog表）
        
        Args:
            minutes: 统计时间范围（分钟）
            
        Returns:
            性能统计数据字典
        """
        cutoff_time = datetime.now() - timedelta(minutes=minutes)

        # 获取时间范围内的API日志
        logs = await APILog.filter(
            create_time__gte=cutoff_time,
            process_time__isnull=False
        ).all()

        if not logs:
            return {"error": "没有最近的性能数据"}

        # 计算基础统计
        process_times = [log.process_time for log in logs if log.process_time]
        response_codes = [log.response_code for log in logs if log.response_code]

        # 按端点分组统计
        endpoint_stats = {}
        for log in logs:
            endpoint = f"{log.request_method} {log.request_path}"
            if endpoint not in endpoint_stats:
                endpoint_stats[endpoint] = {
                    "times": [],
                    "errors": 0,
                    "total": 0
                }
            
            endpoint_stats[endpoint]["total"] += 1
            if log.process_time:
                endpoint_stats[endpoint]["times"].append(log.process_time)
            if log.response_code and int(log.response_code) >= 400:
                endpoint_stats[endpoint]["errors"] += 1

        return {
            "total_requests": len(logs),
            "avg_response_time": sum(process_times) / len(process_times) if process_times else 0,
            "max_response_time": max(process_times) if process_times else 0,
            "min_response_time": min(process_times) if process_times else 0,
            "error_rate": len([c for c in response_codes if c and int(c) >= 400]) / len(response_codes) if response_codes else 0,
            "requests_per_minute": len(logs) / minutes,
            "slowest_endpoints": PerformanceAnalyzer._get_slowest_endpoints(endpoint_stats),
            "status_code_distribution": PerformanceAnalyzer._get_status_distribution(response_codes),
            "endpoint_error_rates": PerformanceAnalyzer._get_endpoint_error_rates(endpoint_stats)
        }

    @staticmethod
    def _get_slowest_endpoints(endpoint_stats: Dict, top_n: int = 5) -> List[Dict]:
        """获取最慢的端点"""
        endpoint_averages = []
        for endpoint, stats in endpoint_stats.items():
            times = stats["times"]
            if times:
                endpoint_averages.append({
                    "endpoint": endpoint,
                    "avg_time": sum(times) / len(times),
                    "count": len(times),
                    "max_time": max(times),
                    "min_time": min(times),
                    "error_count": stats["errors"],
                    "error_rate": stats["errors"] / stats["total"] if stats["total"] > 0 else 0
                })
        return sorted(endpoint_averages, key=lambda x: x["avg_time"], reverse=True)[:top_n]

    @staticmethod
    def _get_status_distribution(response_codes: List) -> Dict[str, int]:
        """获取状态码分布"""
        distribution = {}
        for code in response_codes:
            if code:
                code_str = str(code)
                distribution[code_str] = distribution.get(code_str, 0) + 1
        return distribution

    @staticmethod
    def _get_endpoint_error_rates(endpoint_stats: Dict) -> List[Dict]:
        """获取端点错误率统计"""
        error_rates = []
        for endpoint, stats in endpoint_stats.items():
            if stats["total"] > 0:
                error_rate = stats["errors"] / stats["total"]
                if error_rate > 0:  # 只返回有错误的端点
                    error_rates.append({
                        "endpoint": endpoint,
                        "error_rate": error_rate,
                        "error_count": stats["errors"],
                        "total_requests": stats["total"]
                    })
        return sorted(error_rates, key=lambda x: x["error_rate"], reverse=True)

    @staticmethod
    async def get_api_trends(hours: int = 24, interval_minutes: int = 60) -> Dict[str, Any]:
        """
        获取API性能趋势数据
        
        Args:
            hours: 统计时间范围（小时）
            interval_minutes: 时间间隔（分钟）
            
        Returns:
            趋势数据
        """
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=hours)
        
        # 计算时间间隔数量
        intervals = hours * 60 // interval_minutes
        interval_delta = timedelta(minutes=interval_minutes)
        
        trend_data = []
        
        for i in range(intervals):
            interval_start = start_time + (interval_delta * i)
            interval_end = interval_start + interval_delta
            
            # 获取该时间段的日志
            logs = await APILog.filter(
                create_time__gte=interval_start,
                create_time__lt=interval_end
            ).all()
            
            # 计算该时间段的统计
            process_times = [log.process_time for log in logs if log.process_time]
            error_count = len([log for log in logs if log.response_code and int(log.response_code) >= 400])
            
            trend_data.append({
                "timestamp": interval_start.isoformat(),
                "request_count": len(logs),
                "avg_response_time": sum(process_times) / len(process_times) if process_times else 0,
                "error_count": error_count,
                "error_rate": error_count / len(logs) if logs else 0
            })
        
        return {
            "trend_data": trend_data,
            "interval_minutes": interval_minutes,
            "total_intervals": intervals
        }

    @staticmethod
    async def get_slow_queries(threshold_seconds: float = 2.0, limit: int = 10) -> List[Dict]:
        """
        获取慢查询列表
        
        Args:
            threshold_seconds: 慢查询阈值（秒）
            limit: 返回数量限制
            
        Returns:
            慢查询列表
        """
        slow_logs = await APILog.filter(
            process_time__gte=threshold_seconds
        ).order_by('-process_time').limit(limit).all()
        
        return [
            {
                "request_path": log.request_path,
                "request_method": log.request_method,
                "process_time": log.process_time,
                "response_code": log.response_code,
                "create_time": log.create_time.isoformat(),
                "x_request_id": log.x_request_id
            }
            for log in slow_logs
        ]

    @staticmethod
    async def get_error_analysis(hours: int = 24) -> Dict[str, Any]:
        """
        获取错误分析数据
        
        Args:
            hours: 分析时间范围（小时）
            
        Returns:
            错误分析数据
        """
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        # 获取错误日志
        error_logs = await APILog.filter(
            create_time__gte=cutoff_time,
            response_code__gte=400
        ).all()
        
        # 按状态码分组
        error_by_code = {}
        error_by_endpoint = {}
        
        for log in error_logs:
            # 按状态码分组
            code = str(log.response_code)
            if code not in error_by_code:
                error_by_code[code] = 0
            error_by_code[code] += 1
            
            # 按端点分组
            endpoint = f"{log.request_method} {log.request_path}"
            if endpoint not in error_by_endpoint:
                error_by_endpoint[endpoint] = 0
            error_by_endpoint[endpoint] += 1
        
        # 获取总请求数用于计算错误率
        total_requests = await APILog.filter(create_time__gte=cutoff_time).count()
        
        return {
            "total_errors": len(error_logs),
            "total_requests": total_requests,
            "error_rate": len(error_logs) / total_requests if total_requests > 0 else 0,
            "errors_by_code": error_by_code,
            "errors_by_endpoint": dict(sorted(error_by_endpoint.items(), key=lambda x: x[1], reverse=True)[:10]),
            "analysis_period_hours": hours
        }


class TaskPerformanceAnalyzer:
    """STRM任务性能分析器"""
    
    @staticmethod
    async def get_task_performance_stats() -> Dict[str, Any]:
        """获取任务性能统计"""
        # 获取所有已完成的任务
        completed_tasks = await StrmTask.filter(
            status=TaskStatus.COMPLETED,
            start_time__isnull=False,
            end_time__isnull=False
        ).all()
        
        if not completed_tasks:
            return {
                "total_completed": 0,
                "avg_duration": 0,
                "fastest_task": None,
                "slowest_task": None
            }
        
        # 计算处理时间
        durations = []
        task_stats = []
        
        for task in completed_tasks:
            if task.start_time and task.end_time:
                duration = (task.end_time - task.start_time).total_seconds()
                durations.append(duration)
                task_stats.append({
                    "id": task.id,
                    "name": task.name,
                    "duration": duration,
                    "total_files": task.total_files,
                    "success_files": task.success_files,
                    "failed_files": task.failed_files
                })
        
        if not durations:
            return {
                "total_completed": len(completed_tasks),
                "avg_duration": 0,
                "fastest_task": None,
                "slowest_task": None
            }
        
        # 找出最快和最慢的任务
        fastest_task = min(task_stats, key=lambda x: x["duration"])
        slowest_task = max(task_stats, key=lambda x: x["duration"])
        
        return {
            "total_completed": len(completed_tasks),
            "avg_duration": sum(durations) / len(durations),
            "min_duration": min(durations),
            "max_duration": max(durations),
            "fastest_task": fastest_task,
            "slowest_task": slowest_task
        }


__all__ = ["PerformanceAnalyzer", "TaskPerformanceAnalyzer"]
